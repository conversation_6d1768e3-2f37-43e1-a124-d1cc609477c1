{"name": "frontend-lit-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "tsc": "tsc", "build": "yarn build-graph && yarn lint && yarn tsc && vite build", "preview": "vite preview", "build-graph": "[ -f .env.local ] && set -a && . .env.local; graphclient build", "validate-graph": "[ -f .env.local ] && set -a && . .env.local; graphclient validate", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts --fix", "test": "web-test-runner --node-resolve", "test:watch": "web-test-runner --node-resolve --watch", "clear-vite-cache": "rm -rf node_modules/.vite"}, "dependencies": {"@datastructures-js/priority-queue": "^6.3.2", "@graphprotocol/client-urql": "^2.0.7", "@lit-labs/router": "^0.1.3", "@lit-labs/signals": "^0.1.1", "@lit/context": "^1.1.3", "@lit/task": "^1.0.2", "@reown/appkit": "^1.6.7", "@reown/appkit-adapter-wagmi": "^1.6.7", "@reown/appkit-siwe": "^1.6.7", "@shoelace-style/shoelace": "^2.20.0", "@types/turndown": "^5.0.5", "@vercel/node": "^5.2.1", "@wagmi/connectors": "^5.7.7", "@wagmi/core": "^2.16.4", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "ethereum-blockies-base64": "^1.0.2", "graphql": "^16.10.0", "isomorphic-dompurify": "^2.25.0", "lit": "^3.2.1", "marked": "^15.0.12", "react": "^19.0.0", "turndown": "^7.2.0", "urql": "^4.2.1"}, "devDependencies": {"@graphprotocol/client-cli": "^3.0.7", "@open-wc/testing": "^4.0.0", "@rollup/plugin-alias": "^5.1.1", "@types/mocha": "^10.0.10", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@web/dev-server-esbuild": "^1.0.4", "@web/dev-server-rollup": "^0.6.4", "@web/test-runner": "^0.20.0", "@web/test-runner-playwright": "^0.11.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-chai-friendly": "^1.0.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "sinon": "^19.0.2", "terser": "^5.39.0", "typescript": "~5.7.3", "typescript-eslint": "^8.26.1", "vite": "^6.1.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}